---
title: VMware安装并激活Windows7虚拟机
tags: 虚拟机
abbrlink: 47189
date: 2020-02-05 22:42:54
---

 虚拟机本身是一个软件，可以模拟出一个跟真实环境一模一样的虚拟环境，然后在这个虚拟环境中运行其它软件。 得益于其虚拟化特性，虚拟机广泛应用于软件开发和测试、保护隐私、安全防范等方面，比如可以用它来试用危险的软件、隐匿自己的ip、搭建开发环境。

下面是我用VMware安装并激活Widows7的经验分享！

<!-- more -->

## 获取VMware

直接到VMware的官网下载最新版的Vmware workstation pro安装，然后到网上找一个激活码激活即可。

## 获取Windows7镜像

到“MSDN，我告诉你”网站上下载Windows7原版镜像，这个网站的镜像是纯净原版镜像，安全放心，建议选择旗舰版。

## 安装激活

打开VMware选择刚才下载的镜像，开始安装，我的电脑是8G的内存，2G的独显，所以我分配给虚拟机的内存是4G,显存1G，硬盘是单文件60G（动态占用的，使用多少占用多少），当然啦，划分的硬盘是可以随时可以调整容量大小的，不过值得注意的是，当你扩容你的硬盘时，你还得在虚拟机中用分区工具把扩容部分空间合并到虚拟机的硬盘分区中。在显卡配置中启用3D图形加速，处理器配置中勾选上那三个虚拟化选项，声卡配置中勾选‘启用回声抵消’，不然会出现虚拟机没有声音的问题。

安装完Windows7后就开始激活它吧，将VMBIOS.ROM放到虚拟机文件目录下，注意文件名称大小写，然后在Windows 7 x64.vmx中增加一行：bios440.filename = "VMBIOS.ROM"，最后设置一个共享文件夹将oem_Vista_Win7.exe共享给虚拟机并开启虚拟机运行oem_Vista_Win7.exe，此时会跳出一个命令行窗口，选择厂商，选择序列号，我选的是联想，记得有两步。

## 安装VM-tool

vm-tool是vmware的配套增强工具，安装后可以直接拖拽文件到虚拟机中，共享粘贴板，虚拟机的窗口也可随意调整。不过Windows7直接安装vm-tool是不行的，它必须要Windows7安装有server package 1，那么只需在windows更新中等待sp1的更新，安装sp1后可安装vm-tool。



激活用到的文件我在这里贴出来啦[**激活文件**](https://yadi.sk/d/CsoPpF6tUP4lig)

