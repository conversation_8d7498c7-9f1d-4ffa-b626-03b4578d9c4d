# The URL the site will be built for
base_url = "/"
title = "cylind blog"
author = "cylind"
theme = "zola-futu"

# Whether to automatically compile all Sass files in the sass directory
compile_sass = true

# Whether to build a search index to be used later on by a JavaScript library
build_search_index = false

[markdown]
# Whether to do syntax highlighting
# Theme can be customised by setting the `highlight_theme` variable to a theme supported by Zola
highlight_code = true
highlight_theme = "inspired-github"

render_emoji = true
bottom_footnotes = true

[[extra.nav]]
name = "Home"
url = "@/_index.md"

[[extra.nav]]
name = "Posts"
url = "@/posts/_index.md"

[[extra.nav]]
name = "About"
url = "@/about/_index.md"