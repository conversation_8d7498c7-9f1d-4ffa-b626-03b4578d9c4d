---
title: 个人发卡站解决方案
abbrlink: 57248
date: 2020-10-21 17:55:13
tags: 建站
---

打造一款属于自己的发卡站/小卖铺。

<!-- more -->

## 发卡系统

### 环境配置：

web服务器：

```
sudo apt install nginx
```

数据库依赖：

```
sudo apt install mariadb-server redis-server
```

https://www.digitalocean.com/community/tutorials/how-to-install-and-secure-redis-on-debian-10

php依赖：

```
sudo apt install php-cli php-redis
```

fileinfo检查：https://my.oschina.net/HeAlvin/blog/3075963

opcache扩展：https://learnku.com/articles/18782

composer安装：https://www.runoob.com/w3cnote/composer-install-and-usage.html

### 下载发卡源码

注意点：

1. 更改网站拥有者为www-data
2. 要自己先创建好数据库再配置`.env`

### 支付宝当面付对接

申请当面付

https://openhome.alipay.com/platform/appManage.htm

自研开发

网页&移动应用（会有一个默认的应用2.0）

默认的应用2.0中添加公钥（接口加签）（验证手机号，下载工具，生成密钥对）

在发卡支付网关管理界面，填入

商户id：appid

商户key：支付宝公钥（注意不是自己生成的公钥）

商户密钥：自己生成的密钥

### 添加进程守护

https://learnku.com/laravel/t/3592/using-supervisor-to-manage-laravel-queue-processes

```
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/faka/artisan queue:work --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-queue.log
```

```
mkdir -p /var/log/supervisor/
touch /var/log/supervisor/laravel-queue.log
chown www-data /var/log/supervisor/laravel-queue.log
```

```
# zsh报错的话，请写成bash脚本
supervisorctl start laravel-worker:*

```

```
# 重启服务
supervisorctl restart all
```

常见错误：

https://segmentfault.com/a/1190000007232022