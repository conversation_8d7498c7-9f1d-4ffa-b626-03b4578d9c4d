---
title: 视频切片及免费托管
date: 2021-02-23 01:19:51
tags: 奇技淫巧
---

利用Google Cloud Shell，Colab等免费服务（配置好、高性能）或者用自己闲置的VPS将视频切片好后上传Github，可以实现视频存储和在线播放。

使用Google Cloud Shell时，为了防止掉线，可利用Tmux配合Htop，将屏幕分屏，一边运行Htop，一边执行其他任务。

<!-- more -->

## 视频下载

下载器推荐aria2, 因为最常见的http、torrent、magnet下载方式它都支持，而且体积小巧功能强大。使用aria2可以直接在命令行设定下载参数，也可以使用`--conf-path` 来指定`aria2.conf`下载参数配置文件，推荐后者，因为方便管理。

下载磁力时，添加一些好的tracker往往能加速你的下载，这里推荐 https://trackerslist.com/best_aria2.txt 值得注意的是，aria2添加tracker有特定的格式，`bt-tracker=tracker1,tracker2,tracker3...` ，而巧的是上面提供的链接提供的就是这种格式，无需转换了。

寻找视频可以去常见的BT/磁力资源站，比如JAVBUS等，也可以利用google搜索`index of keyword`，比如`index of ssni`来发现别的小伙伴的个人资源站点，这些资源站点往往都是可以http直链下载，速度超级快。

## 视频切片

如果视频是h264编码，音频是acc编码，封装格式为mp4的话，可以不转换编码直接切片，速度非常快。如果源视频不是mp4格式，比如mkv或者flv，可以先转换格式，这个过程可能比较漫长，往往转换时间差不多是源视频时长的两倍，也可以和前面一样不转换格式直接采取copy编码的方式切片（但这样可能在网页播放不了，需要用本地播放器播放）。

### 不转码直接切片

```bash
ffmpeg -i output.mkv -c copy -f hls -hls_time=5 -hls_list_size 0 -hls_segment_filename hls%4d.ts index.m3u8
```

`-c  copy`表示保留源视频的音频、视频的编码格式，不做转换。

`-f hls` 表示指定封装格式为hls

`-hls_time=5` 表示指定切片长度（秒），默认为2秒，越小加载就越快。

`-hls_list_size 0` 设置播放列表保存的最多条目，设置为0会保存有所片信息，默认值为5。

`-hls_segment_filename hls%4d.ts` 指定生成的切片的文件名，`%4d` 表示4位数字。

### 转为h264/aac编码的mp4后再切片

#### 一、将视频转为h264/aac编码的mp4文件   

可以预先使用ffprobe查看文件编码方式

```
 ffprobe input.mkv 
```

如果得到音视频编码为h264/aac则执行

```
 ffmpeg -i input.mkv -c copy out.mp4
```

否则执行

```
 ffmpeg -i input.mkv -c:v libx264 -c:a libfaac out.mp4
```

其中，`-c:v`和`-c:a`分别表示视频编码、音频编码，其后接对应的编码器；若只写`-c` 则表示音频和视频采用同样的编码参数，比如`-c  copy`表示保留源视频的音频、视频的编码格式，不做转换。

#### 二、将mp4文件切片并生成m3u8

```bash
ffmpeg -i output.mp4 -c copy -f hls -bsf:v h264_mp4toannexb -hls_time=5 -hls_list_size 0 -hls_segment_filename hls%4d.ts index.m3u8
```

`-bsf:v h264_mp4toannexb` 指定h264以字节流AnnexB格式封装。

H264有两种封装方式：字节流AnnexB格式， AVCC格式。AnnexB格式用于实时播放，AVCC格式用于存储。

注意，上述参数中，视频片段后缀可随意发挥，比如将 `ts` 改为 `js` 。但还是m3u8后缀名不可更改，否则不会生成对于的m3u8格式文件，等生成完毕m3u8文件后，可随意更改后缀而无影响。

### 参考

https://www.ruanyifeng.com/blog/2020/01/ffmpeg.html

https://blog.cauchyschwarz.com/blog/note/2020/09/05/ffmpeg.html

https://www.cnblogs.com/fieldtianye/p/13427303.html

https://www.webzhishi.com/ffmpeg-tt-m3u8/

https://blog.csdn.net/hejjunlin/article/details/71001593

https://blog.csdn.net/amazing_yangle/article/details/49029687

https://www.cnblogs.com/vczf/p/13818609.html

https://github.com/aisuhua/ffmpeg-demo

https://www.daguanren.cc/post/FFmpeg.html

https://docs.peer5.com/guides/production-ready-hls-vod/

## 视频发布

切好的视频可以发布到网上方便观看和分享。大多数数网友都是扒免费图床的API，通过将TS视频片段伪装成图片后上传到图床来实现视频托管，不少个人视频站都是这么干的。这里介绍的是上传到Github的方法，因为Github不仅稳定，而且有不少Github加速工具可以用，比如Jsdelivr cdn，此外不少静态托管网站也对接了Github，一键部署，比如vercel，vercel有cdn支持而且每月提供100g流量，自用的话够了。最关键的是，视频的掌控权还在自己手里，想要修改内容可以git clone下来，想要移除视频可以直接删库或改为私有仓库，想要再发布到其他平台可以方便的clone然后上传。

### Git设置

设置本地ssh和git环境

```bash
mkdir -p ~/.ssh/
echo "$SSH_PRI_KEY" > ~/.ssh/id_rsa
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_rsa
ssh-add ~/.ssh/id_rsa
ssh-keyscan github.com >> ~/.ssh/known_hosts
git config --global user.email "<EMAIL>"
git config --global user.name "chan"
```

### 上传到Github

先通过API或其他方式创建一个Github仓库，这里给出API方式创建Github仓库的一个示例

```bash
curl \
-X POST \
-H "Authorization: token $ACCESS_TOKEN" \
-H "Content-Type: application/json" \
https://api.github.com/user/repos \
-d "{\"name\": \"$repo\"}"
```

然后将切好的视频片段连同m3u8文件一起push到Github。值得注意的是，Git push时会把所有文件打包成一个大文件上传，而Git传输对单文件大小有限制，即使使用git-lfs能上传，限制仍然非常多。我们可以通过每次commit push上传一小部分文件，多次push，解决这个问题。使用循环语句解决方案如下：

```bash
for i in {0..9};do
  for j in {0..9};do
    files=(index$i$j??.ts)
    if [ -f ${files[0]} ];then
      git add "${files[@]}"
      git commit -m "$i$j st commit"
      git push
    else
      git push
      exit 0
    fi
  done
done
```

参考：

https://docs.github.com/en/rest/guides/getting-started-with-the-rest-api

https://my.oschina.net/eduOSS/blog/287824
