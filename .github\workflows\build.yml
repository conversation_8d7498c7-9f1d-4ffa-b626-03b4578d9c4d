name: <PERSON><PERSON> on GitHub Pages

on: 
 push:
  branches:
   - main

jobs:
  build:
    name: Publish site
    runs-on: ubuntu-latest

    steps:
    - name: Checkout main
      uses: actions/checkout@v5
      with:
        submodules: true

    - name: Build and deploy
      uses: shalzz/zola-deploy-action@v0.21.0
      env:
        TOKEN: ${{ secrets.ACCESS_TOKEN }}
        REPOSITORY: cylind/cylind.github.io  
        PAGES_BRANCH: master