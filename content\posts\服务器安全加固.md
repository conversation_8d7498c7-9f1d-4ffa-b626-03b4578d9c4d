---
title: 服务器安全加固
tags:
  - vps
  - 信息安全
  - Linux
abbrlink: 6542
date: 2020-06-14 23:06:07
---

之前的一台小鸡，从早被扫到晚，以至于我ssh时常掉线，自那以后开始了解到ssh爆破这个概念，上网搜集了相关教程，汇总了一下。当然啦，用秘钥登录是最安全的，我这里最求方便，折中一下，介绍的是密码登录的安全加固方法，简单几步，让你的服务器安全系数瞬间提升。

<!-- more -->

## 启用强密码

启用强密码可以有效防止密码爆破，就是防止别人用字典轻易套出你的密码，现在ssh爆破，大都有比较好的字典，弱密码的话，分分钟可能就被爆破了，所以强烈建议使用强密码：

```bash
passwd root
```

## 更换ssh端口

网络上的ssh爆破一般都是扫默认的22端口，如果我们把ssh端口改一下，肯定地说，可以过滤掉一大片低端ssh爆破扫描了，另外，大部分ssh爆破扫描都是扫描1000以下的低端口，所以建议更换ssh端的话，更换到1000以上的高端口。

```bash
nano /etc/ssh/sshd_config
Port = [yourPort]
service sshd restart
```

## 开启防火墙

安装防火墙，把不用到的端口关掉，从而把攻击面降到最小。ufw是比较好用的防火墙工具，推荐使用。

```bash
apt install ufw
ufw allow 80
ufw allow 443
ufw allow [yourSSHPort]
ufw enable
ufw delete allow 80
```

## 防止SSH爆破

ssh爆破就是不断地用字典中的密码来试探你的真实密码，这时候如果我们限定密码错误次数，如果达到一次的错误次数，就封禁ip，也能很有效的防止密码爆破。fail2ban可以实现我们需要的功能，它能通过分析登录日志，从而根据我们预设的规则做出相应的动作。

```bash
apt install fail2ban
cd /etc/fail2ban
cp jail.conf jail.local
nano jail.local
# 排除自身ip，ignoreip处加上自己的ip
# sshd处更改端口为2333，并加上 enabled = true
# 然后启动fail2ban服务，并设置开机自启
service fail2ban restart
systemctl enable fail2ban
```

## 定期检查登录日志

觉得不太对劲的时候，可以查查登录日志。这里是举例的是Debian系的登录日志，centos的可以上网查。

```bash
cat /var/log/auth.log | grep Accepted
cat /var/log/auth.log | grep Failed
```

## 补充：秘钥登录

某些大厂商，比如AWS,GCP等，默认要求用户用秘钥登录，以提高安全性。那么这时候，我们用秘钥登录后，可通过添加新用户并授予sudo权限，修改`sshd_config`设置来进行密码登录，更方便地管理机器。

秘钥登录:

```bash
ssh -i my-key-pair.pem username@server_address
```

允许密码登录：

（有的机器可能默认允许密码登录，无需修改）

```bash
nano /etc/ssh/sshd_config
PasswordAuthentication yes
```

添加新用户并添加到sudo组：

```bash
adduser newuser
usermod -a -G sudo newuser
```

## 参考：

https://liamlin.me/2018/11/27/security-hardening-guide-on-centos-7