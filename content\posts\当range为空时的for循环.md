---
title: 当range为空时的for循环
abbrlink: 47430
date: 2020-04-16 20:49:46
tags: Python

---

我们知道在python中for循环常常和range函数一起用，如`for i in range(100):` ,其中range函数有由三个参数控制，起始值、终止值、步长，返回的是一个可迭代对象,该对象并非列表，而且是左开右闭的，也就是右边的数是不取的，比如range(2,3),那么能取到的值只能是2。

在素数判断中，我们判断一个数是否是素数，我们需要对这个取余，从2开始一直到这个数的平方根，如果出现余数为零的情况则判断它不是素数。那问题来了, 如果判断的数从2开始，那该从range(2,2)里遍历，注意到没有range(2,2)的左右边界是相等的，那么它会返回结果包含哪些数呢？还是会报错？

<!-- more -->

```python
n = eval(input())
for i in range(2,n+1):
    for j in range(2,int(i**0.5)+1):
        if i%j==0:
            break
    else:
        print(i)
```

当range函数的右边界小于等于左边界时（步长为正的情况下），并不会报错，返回的仍是range对象，用list方法将其转变为列表时，返回的是一个空列表，这种情况下搭配for循环使用，并不会执行for语句下对应的内容，即在上述代码中第二个for语句下的if语句不会被执行；如果for还搭配了else，则直接执行else语句下对应的内容，即上述代码中的`print(i)` 被执行。
