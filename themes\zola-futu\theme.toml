name = "futu"
description = "Futu Theme"
# An optional tags to allow quick search
tags = []
license = "MIT"
homepage = "https://github.com/inhzus/zola-futu/"
# The minimum version of Zola required
min_version = "0.20.0"
# An optional live demo URL
demo = ""

# Any variable there can be overridden in the end user `config.toml`
# You don't need to prefix variables by the theme name but as this will
# be merged with user data, some kind of prefix or nesting is preferable
# Use snake_casing to be consistent with the rest of Zola
[extra]

# The theme author info: you!
[author]
name = "inhzus"
homepage = "https://inhzus.io/"

# If this is porting a theme from another static site engine, provide
# the info of the original author here
[original]
author =  "yuanji"
homepage = "https://blog.yuanji.dev/"
repo = "https://github.com/yuanji-dev/futu"
