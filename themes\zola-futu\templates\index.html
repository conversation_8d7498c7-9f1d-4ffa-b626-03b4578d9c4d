{% extends "base.html" %}

{%- block init -%}
  {{- super() -}}
  {%- set title = config.title -%}
{%- endblock init -%}

{%- block main -%}
    <article class="home-section">
      <h2>Latest Posts</h2>
      {%- set posts_section = get_section(path="posts/_index.md") %}
      <ul class="pages-list">
      {%- for post in posts_section.pages | sort(attribute="date") | reverse | slice(end=5) %}
        <li>
          <a href="{{ post.permalink | safe }}">{{ post.title }}</a>
          <small><time datetime="{{ post.date }}">{{ post.date | date(format="%b %d, %Y") }}</time></small>
        </li>
      {%- endfor %}
        <li><a href="{{ posts_section.permalink | safe }}">View All →</a></li>
      </ul>
    </article>
{%- endblock %}
