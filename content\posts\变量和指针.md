---
title: 变量和指针
date: 2021-03-09 14:29:54
tags: 计算机系统
---

看了计算机系统课程的"从底层来了解指针"补充视频后，对变量和指针的概念有了一个更清晰的印象。下面，我将用自己的话来说说我对指针和变量的理解。

<!-- more -->

## 变量

变量可分为两种，一般变量和指针变量。无论是那种变量，都会包含三个信息，类型、值和内存地址。所谓的内存地址就是我们常说的指针，其实我们常说的指针、地址、内存地址都是表达的同一个东西。

其中，变量类型用来确定变量的大小，也就是用来确定应该给这个变量分配多少个字节的内存。

而变量的内存地址，就是操作系统分配这个变量的内存空间的首字节的虚拟内存地址。是的，内存中的每个直接都有对于的物理地址和虚拟地址，其中物理地址就是地址总线寻址时所传来的编号；而虚拟内存地址，由操作系统以通过进程的概念，给程序分配的看似连续的一片字节编号（我现在对进程和虚拟内存还不是很了解，等以后再做补充完善）。

变量中的值，就是这个变量所分配到的内存空间中的一堆二进制数字，至于它表示什么，取决于你以何种方式去看待它。若以整数 `%d` 的方式来 `printf`  它，系统就会从该变量的首地址开始取4个字节（int类型大小与操作系统有关，这里仅仅是举例），然后以规定的整数的表示方式进行解读并将结果展示出来；若以字符串 `%s` 的方式 `printf` 它，系统就会从该变量的首地址开始逐个字节地取数据解读出对应的整数，并对照acsii表将字符打印出来，直到遇到0。

指针变量的值，存放的是内存地址。内存地址的大小和操作系统有关，32bit的操作系统一个地址占32个bit，即4个字节。

## 数组和指针

数组是个特殊的变量，它由一些系列类型相同的变量按顺序排列而成，字符串是数组的一个特例。数组名前面的字符定义数组中变量的类型，比如`int * array[10]` 表示 数组中的变量为指向整型的指针 。

数组名作为变量时，表示的是一个指针，也即数组的首地址。而在数组名加下标的形式，比如 `array[0]` ，则表示的是将数组首地址作为基本地址（基址）加上数组下标作为偏移量（偏移地址，偏址）所构成的合成地址对应的变量。

```c
#include <stdio.h>

int main(int argc, char const *argv[])
{
	int array[3]={1,2,3};
	// 输入数组即输入数组的首地址
	printf("%d\n", array[0]);
	// 输入array[0]即输入对应地址上的变量，相当于*p
	printf("%p\n", array);
	return 0;
}
```



指针变量虽然有类型，但是也能将不同类型的指针（地址）赋值给它，因为都是地址，地址的大小也是一样的。但是试图通过 `*p`  （假设 `p` 是一个指针） 给指针对应的变量赋值时，赋的值会被强制转换为指针 `p` 所声明的类型。

```c
int i = 1532;
float * fp;
*fp = i;
// *fp对应的变量将会是浮点型的1532.0
```

