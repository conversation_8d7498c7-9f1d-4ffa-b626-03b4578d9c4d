---
title: 机器学习笔记（一）
tags:
  - Python
  - 机器学习
abbrlink: 29409
date: 2020-02-06 22:48:02
---

开始入坑机器学习啦，大体的入门学习路线就是看完吴恩达的机器学习、深度学习这两个课程，并完成其课程作业。学习笔记会更新在这里，就相当是一个复习总结的过程吧！

<!-- more -->

# 机器学习简介

## 定义

  A computer program is said to learn from experience E with respect to some task T and some performance measure P,if its performance on T,as measured by P,improves with experience E. 

## 分类

机器学习主要分为监督学习和无监督学习两大类

### 监督学习

 监督学习（supervised learning）:监督学习就是给出一组特征，也给出特征所对应的结果。以此来推测另外的特征所对应的结果。 

监督学习又可以划分为两种，回归分析和分类。回归分析中比较容易理解的一维的就是线性预测嘛，比如标准曲线法就是典型的线性预测，当然还有维度更高的啦，道理都是一样的。分类就很简单啦，分两类的话就是判断是非对错，分多类也一样。

### 无监督学习

 无监督学习（unsupervised learning）:无监督学习就是给出一堆带有某些特征的数据，但是不给出这些特征所对应的结果，以此来判断这些不同的特征之间有什么结构关系。聚类问题就是无监督学习的一个例子，说白了就是给你一堆数据，然后让算法自动给数据分门别类。 

# 单变量线性回归

假设方程中只含有一个特征/输入变量的问题叫作单变量线性回归问题。

### 符号定义

m：样本数量（ training examples）

x：输入值，又成为特征（input variables/features）

y：输出值，又叫目标值（output variables/target variables）

(x,y)：训练样本（training examples）

第i个训练样本（i<sup>th </sup>training examples）：（x<sup>(i)</sup>,y<sup>(i)</sup>)

h 代表学习算法的解决方案或函数也称为假设（**hypothesis**）

### 模型表示

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/1.png)

### 代价函数

假设方程中有两个参数是需要我们确定的，我们如何选择这两个参数将直接影响到假设方程预测的准确度。

我们将假设方程预测的值与样本真实值之差称为**建模误差**，显然建模误差之和越小，则预测越准确，我们要做的便是确定两个参数的值以使得建模误差之和最小，因此我们建立以上述两参数为变量**代价函数**（平方差函数），这样能使得代价函数取得最小值的点即所求。

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/建模误差.png)

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/代价方程与假设方程.png)

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/拟合误差0.png)

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/拟合误差.png)

### 梯度下降

梯度下降是一个用来求函数最小值的算法，使用梯度下降算法可以求出代价函数 的最小值。
![批量梯度下降](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/批量梯度下降.png)
梯度下降背后的思想是：开始时我们随机选择一个参数的组合，计算代价函数，然后我们寻找下一个能让代价函数值下降最多的参数组合。我们持续这么做直到到到一个局部最小值（**local minimum**），因为我们并没有尝试完所有的参数组合，所以不能确定我们得到的局部最小值是否便是全局最小值（**global minimum**），选择不同的初始参数组合，可能会找到不同的局部最小值。<mark>实际上，在这里只有一个最小值，所以无需考虑它是否是全局最小值</mark>

![同步改变](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/同步改变.png)

批量梯度下降（**batch gradient descent**）算法的公式为： 其中是学习率a（**learning rate**），它决定了我们沿着能让代价函数下降程度最大的方向向下迈出的步子有多大，在批量梯度下降中，我们每一次都同时让所有的参数减去学习速率乘以代价函数的偏导数。学习率a设得过大则会一下子迈过了最小值，设置得过小需要迈的次数则太多，耗费时间。<mark>参数同时变化的，不能先求出一个参数代入到代价方程中去求另外一个参数</mark>

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/梯度下降.png)

### 梯度下降的线性回归

**批量梯度下降**指的是在梯度下降的每一步中，我们都用到了所有的训练样本，在梯度下降中，在计算微分求导项时，我们需要进行求和运算，所以，在每一个单独的梯度下降中，我们最终都要计算这样一个东西，这个项需要对所有m个训练样本求和。因此，批量梯度下降法这个名字说明了我们需要考虑所有这一"批"训练样本，而事实上，有时也有其他类型的梯度下降法，不是这种"批量"型的，不考虑整个的训练集，而是每次只关注训练集中的一些小的子集，比如正规方程。

求导后的参数形式：

![求导后的参数形式](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/求导后的参数形式.png)
