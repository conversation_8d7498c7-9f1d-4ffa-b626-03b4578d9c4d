---
title: 超星网盘的妙用
tags: 奇技淫巧
abbrlink: 50657
date: 2020-02-11 23:43:17
---

2025.9.16更新：p.ananas.chaoxing.com也有refer防盗链验证，不建议作图床使用，建议使用picgo + github



找到一款好的网盘实在是越来越难了，国内的网盘毫无隐私安全可言（一般也就当个资源网盘用），而且要么存储空间小，要么下载速度慢，就拿百度云盘来说，不仅限速，而且你还得安装客户端才能下载文件；国外的网盘吧，一来空间也小，二来它服务器在海外访问速度还是不是稳定。

找到一个好的图床也越来越难，国内的图床，没有安全隐私可言咱也就不说了，毕竟也没有那个不开眼的企图在国内的网站上储存啥违规图片，关键是没得用，新浪图床开启了防盗链验证，免费图床的大好日子一去不复返啦，国外的图床，一个字，慢！

废话不多说，今天俺发现了超星网盘的上述两大用处，基本可以实现网盘使用、图床使用功能，超星网盘150G大容量，上传下载不限速，单文件限制2GB，经营多年，安全可靠，用它做网盘和图床问题不大！

<!-- more -->

![网盘界面](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/a6090cfd24c85f88d7c6cb9f85eea887.png)

先说一下实现的具体思路，当我们把文件上传超星网盘时，它会先检测一下文件hash值，如果服务器中已经有了这个文件，那它是不会上传你的文件的，然后做做样子，假装在急速秒传，是的，很多文件传输服务都是这么干的，包括QQ文件传输、yandex disk等。如果服务器中没有这个文件，它则会上传这个文件并记录下文件的hash值（标记为object_id），同时生成一个十进制的文件标识码（标记为node）。

分享文件时用node标识码作唯一识别，直链下载文件时用object_id作唯一标识，看到这里大家应该就会发现了，只要我们获得它的node和object_id就可以构造出下载链接并实现云盘直链下载以及图床功能了。我试着用hash检测工具检测文件hash值并和文件的object_id对比，常用的md5、sha1、sha256都对不上，然后我试着在网盘文件界面和分享页面查看查看网页元素，结果发现，node和object_id就在那里，白纸黑字，都不屑于隐藏，嗯，问题解决了。

## 图床实现

找到目标图片，右键检查，object_id和图片预览链接原形毕露，如图：![获取objectid](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/f721f79721fd2fa9abfb038db2a2ea69.png)

网页源码如下，其中`?type=img` 前面的`f721f79721fd2fa9abfb038db2a2ea69`即为object_id

```html
<span class="ypImg"><img src="http://pan-yz.chaoxing.com/thumbnail/40,40,50/f721f79721fd2fa9abfb038db2a2ea69?type=img" onerror="javascript:this.src='/views/images/filelogo/x_pic.png';" width="36" height="logo_heigth"></span>
```

复制上述代码中的src后的链接(预览图链接)到浏览器打开，再从链接中提取出原图链接`http://p.ananas.chaoxing.com/star3/origin/1be4e1c714cdb396c9c96331a2741574.png`

获得的直链可以直接在markdown下显示图片啦！我现在写的这篇文章使用的全部图片都是源于超星云盘上的，效果如你所见。

<mark>注意，不要用d0.ananas.chaoxing.com的前缀来构造图片直链，会有refer防盗链验证的</mark>

## 网盘实现

网盘直链下载比图床实现稍稍复杂了一点点，不过都是一样的思路。手机端的超星助手可以直接分享文件给别人，别人无需登录即可下载，但网页端的话则不行。

同样的找到目标文件，右键检查，发现node如下：![获取node](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/ba9689d5d4799000b01672748eb2788f.png)

图中带有node_name_后接的一串数字即是node，如你所见node为`434108759749431296`，构造无需登录可下载的链接，特定前缀`https://pan-yz.chaoxing.com/external/m/file/` 加上node，如下：

```
https://pan-yz.chaoxing.com/external/m/file/439138726555971584
```

浏览器中打开该链接，下载按钮处右键检查，展开`<script type="text/javascript">` 标签，如图:

![获取下载直链](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/0d90b2ff609ee0dc2e82fd46824d4085.png)

直接获得download对应的下载直链。

当然啦，也可以获取object_id后用如下前缀构造直链：

```
http://p.ananas.chaoxing.com/star3/origin/
```

## 常见问题

### 单文件超出~~2GB~~ 1GB大小

可以用压缩软件7-zip或WinRAR进行分卷压缩，将文件分为几部分上传。

### 管理员能随意查看上传文件

可以将隐私文件隐藏加密后隐藏在图片中，或者直接压缩加密。话说回来，超星网盘是适合做资源盘的，不适合做同步盘或者备份盘的，所以还要啥自行车!

### 网速时快时慢

~~是的，我也发现这个问题啦，它应该是限制了多线程下载，大多数时候只能连接到一个线程下载，偶尔能用两个线程，而且每个文件的下载速度不一样，有的单线程下载就特别快能跑满带宽，有的则时快时慢，有的则没有速度，有的还必须待fn参数速度才快，有的带不带都快，总的来说，连接不是特别稳定，可能和这段时间大量视频直播有关吧，再等等看，如果还是只有俺就弃坑啦！~~ 大体确定是因为超星直播流量太大影响到了云盘功能，并没有限制多线程下载，速度还是很快的！即使是单线程下载速度还是快得一批！可能是用的人少吧！

![](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/1be4e1c714cdb396c9c96331a2741574.png)

### 不支持https？

看起来是的，我们获取到的链接的却是http直链，但是咱可以自己加个s上去，我试过啦，完全没有问题的。超星网盘的主页也是一样的道理，自己加上s即可：https://pan-yz.chaoxing.com/

### 150G还是不能够用？

注册小号，https://passport2.chaoxing.com/register2，注意啦，register后面不带数字或带的数字不是2的都是要到手机号才能注册的，register2则可以用邮箱注册！
