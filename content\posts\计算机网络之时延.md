---
title: 计算机网络之时延
date: 2021-03-10 08:35:47
tags: 计算机网络
---

计算机网络采用**分组交换**来传输数据，即**分组-存储-转发**三部曲，分组交换过程大体如下所述：

首先，主机将所要传输的数据拆分成若干个一定大小的数据块，并给每个数据块加上额外的头部（表头）和尾部（表尾），头部包含有数据块编号、目的地址等信息，而尾部含有差错控制相关的信息（用于校验数据包是否完整，校验数据段和表头），这样就等到了一堆含有表头、数据段和表尾的数据包。每一个这样的数据包称为一个分组（packet），这个处理过程也叫**分组（packeting）**。

<!-- more -->

接着，主机将每个分组发送到物理链路上，通过物理链路到达路由器。路由器接收到分组后，开始检查表尾（判断数据包是否完整），检查表头（查看其目的地址，并根据路由表决定该将这个分组发送到哪个出口），然后将其存储到路由器的发送队列上。这个处理分组所产生的时延就叫**处理时延**。

如果已经有其他分组在发送队列中了，那么当前分组就要在发送队列中排队，直到前面的分组发送完毕才能轮到它被发送，这个过程产生的时延叫**排队时延**。

一个分组从路由器发送队列中经发送器作用被发送到传输介质（比如光纤、双绞线等）所需的时间称为**发送时延**（也叫传输时延，transmission）。

发送过程其实就是将分组编码为信号波（电磁波、光信号等）的过程。信号波的频率越高，单位时间内的信号波所包含的信息也就越多，所以发送速率也就越快，发送时延也就越低。所以要提高传输速率，或者说提高带宽的关键是提高信号频率。发送时延与发送器的功率（要能产生高频信号）、传输介质的性质（要能容纳得下高频信号）相关，主要取决于传输介质。光纤比双绞线更适合传输高频信号，所以网络核心，也就是路由器与路由器之间的通信，都是采用光纤通信的。

最后，发送器产生的信号在传输介质上飞奔到下一台路由器或主机所耗费的时间就是**传播时延**，也就是两个主机或路由器间的物理距离除以光速。

以上就是计算机网络分组交换的各个过程所产生的时延，而总时延就等于这些时延之和。有一些值得注意的小细节是，我们并不可能准确地计算出总时延，因为其中有一些东西是很难计算或无法计算的，所以结果只能是一个大概的值。比如传播时延我们就用两主机间的物理距离除以光速，它也就只是一个大概的值，因为你第一个比特发送到目的主机后，还有好多比特还在路上，甚至都还没出发，这些时间都没有计算到。

最后的最后，一个小问题，好多人都说发送速率和传播速率毫无关联，不要把它们混淆了。确实，在概念不相同，但是实际上他们是有关联的啦，你想啊，电磁波频率越高传波速率就越快，而前面也说了信号频率越高发送速率也就越快，所以说，在同一传输介质中，发送速率越快，传播速率也就越快，虽然说关系可能不是那么明显。
