---
title: 80386寄存器概览
date: 2021-03-21 10:17:50
tags: 计算机系统
---

占坑～～

学习计算机系统，感觉老师汇编部分讲的不多，寄存器那里主要介绍那几个通用寄存器，段地址寄存器没有讲到，产生了一些疑惑，所以我决定在这里给它整明白了。

<!-- more -->

当前疑惑的主要是段地址的区别，8086和80386的段地址寄存器都是16位的，二者的寻址方式也不一样了，8086是 `段地址×16 + 偏移地址` （结果是逻辑地址），80386段寄存器里装的不是段偏移地址，而是一个段描述符，根据描述符取得物理地址基址，加上偏移算出物理地址描述符表定义所有段的物理地址基址。

## 参考

[80386常用内部寄存器](https://linux.cn/blog-11720-5749.html)

[80386汇编](https://www.cnblogs.com/l0nmar/p/12553872.html)

[重新认识intel段机制寻址方式](https://programtip.com/en/art-48102)