---
title: 机器学习笔记（二）
tags:
  - Python
  - 机器学习
abbrlink: 58011
date: 2020-03-02 21:14:02
---

# 多变量线性回归

多变量线性回归和单变量线性回归大同小异啦

## 符号定义

m表示训练集的规模，n表示样品的特征数量，x的上下标分别表示第几个样品的第几个特征

![多变量线性回归表示](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/多变量线性回归表示.png)

假设方程表示如下：

![多变量线性回归方程](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/多变量线性回归方程.png)

注意啦，为了方便计算我们默认x<sub>0</sub> = 1

其向量表示形式如下：

 ![多变量线性回归方程的向量形式](https://raw.githubusercontent.com/cylind/cylind.github.io/static/img/多变量线性回归方程的向量形式.png)

## 梯度下降
