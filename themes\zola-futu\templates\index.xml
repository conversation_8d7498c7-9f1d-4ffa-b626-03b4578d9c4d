<?xml version="1.0" encoding="UTF-8"?>
<rss xmlns:atom="http://www.w3.org/2005/Atom" version="2.0">
    <channel>
      {%- if config.extra.follow %}
      <follow_challenge>
        <feedId>{{ config.extra.follow.feedId }}</feedId>
        <userId>{{ config.extra.follow.userId }}</userId>
      </follow_challenge>
      {%- endif %}
      <title>{{ config.title }}
        {%- if term %} - {{ term.name }}
        {%- elif section.title %} - {{ section.title }}
        {%- endif -%}
      </title>
      <link>
        {%- if section -%}
          {{ section.permalink | escape_xml | safe }}
        {%- else -%}
          {{ config.base_url | escape_xml | safe }}
        {%- endif -%}
      </link>
      <description>{{ config.description }}</description>
      <generator>Zola</generator>
      <language>{{ lang }}</language>
      <atom:link href="{{ feed_url | safe }}" rel="self" type="application/rss+xml"/>
      <lastBuildDate>{{ last_updated | date(format="%a, %d %b %Y %H:%M:%S %z") }}</lastBuildDate>
      {%- for page in pages | slice(end=5) %}
      <item>
          <title>{{ page.title }}</title>
          <pubDate>{{ page.date | date(format="%a, %d %b %Y %H:%M:%S %z") }}</pubDate>
          <author>
            {%- if page.authors -%}
              {{ page.authors[0] }}
            {%- elif config.author -%}
              {{ config.author }}
            {%- else -%}
              Unknown
            {%- endif -%}
          </author>
          <link>{{ page.permalink | escape_xml | safe }}</link>
          <guid>{{ page.permalink | escape_xml | safe }}</guid>
          <description xml:base="{{ page.permalink | escape_xml | safe }}">{% if page.summary %}{{ page.summary }}{% else %}{{ page.content }}{% endif %}</description>
      </item>
      {%- endfor %}
    </channel>
</rss>
