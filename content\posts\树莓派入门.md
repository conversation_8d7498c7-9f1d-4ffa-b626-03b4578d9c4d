---
title: 树莓派入门
tags: 树莓派
abbrlink: 63228
date: 2020-04-17 16:56:40
---

树莓派(Raspberry Pi)是一款基于ARM的微型电脑主板，旨在促进落后地区学生计算机编程教育。虽然树莓派只有一个卡片大小，但是计算机该有的功能它一样具备，加之它能耗小能够长时间待机，而且价格便宜，因此树莓派不少搞机人士的青睐。

本文主要介绍一下，我购买树莓派4b的基本配置及其用途，当然，还会现身说法，讲述一下我踩过的坑。

<!-- more -->

## 树莓派购买及组装

组装一个树莓派的必备组件有主板、盒子、micro-sd卡、电源，其它配件可选。

树莓派官方售价是2G RAM 35美元, 4G RAM 45美元，可以到淘宝或京东上购买（在淘宝上买，店家一般都会送入门资料），注意，这是裸板的价格，并不含盒子、电源和 micro-sd卡的，还有一点值得注意的是，如果买的是4G内存的板子记得问店家发的是不是1.2升级版主板哦，1.1版的在某些type-c线插入时会有bug，2G内存的没有升级版呢，所以2G内存的板子价格跌得也比较快。

个人建议板子和其他配件分开买，比较划算，买套餐的话会比较坑。micro-sd卡要买c10以上的16g存储以上的，这样系统会比较流畅（c10的含义是指最低读写速度不低于10m/s,同理得c4，c6，关于micro-sd卡规格的详细说明请自个儿Google）。

电源，5v-3a的type-c线即可，电流电压低了，系统会运行不稳定，建议买那种带开关的，就可以不用频繁插拔，频繁插拔对type-c电源接口有磨损的。关于手机type-c充电线可不可以给树莓派供电的问题，答案是可以的，只要电压电流不低于官方要求的就行了，问题手机的充电器一般都比树莓派的贵啊，如果你有闲置的type-c充电线，你也可以不买。

盒子，最好是买带散热片和小风扇的那种，如果你想你的树莓派长时间稳定运行的话，我个人卖的是透明的九层亚克力盒子，带小风扇和散热片。

买完上述这些东西就开始组装吧，组装是有讲究的，特别是那个九层的盒子，保护膜是要撕下来的。。。刚拧上螺丝，结果发现不对，还得拆开，弄了一上午，没搞定。。。后来还是到网上看了教程视频才搞定的，这个告诉我们，办事之前要做好充分准备，不要脑子一热，直接就上，这样真的费时费力，所以盒子的组装还是建议大家对着视频来，有一点要注意的是，小风扇的组装，那个针脚的插法，红线插在第2针，黑线插在第4针，小风扇有字那一面朝向主板cpu，分不清的话，先别装最后一层，直接通电，用手感受一下，那边是出风口那边就朝向cpu就对了。

## 系统烧录

树莓派的系统是烧录在micro-sd卡上的，这也就是为什么建议买c10以上规格的卡的原因啦，如果卡的读写速度慢，那么你的体验自然没有那么好。

关于镜像选择的问题，我还是建议先选官方推荐的基于debian的raspbian（带桌面那款），因为用的人多，教程文档自然也多，很多坑别人已经踩过，甚至已经填好了，再一个就是稳定，虽然它是32bit的系统（可以选择开启64bit内核，但由于文件系统还是32bit的，所以依然还是无法安装64bit的软件，不建议开，开了速度提升并没有感觉到，反而部分32bit的deb包不能正常启动）。等熟悉了以后，有其它需求了，再换其它操作系统。

在官网下载完成系统镜像后，用u盘工具（rufus和官方推荐的Win32DiskImager）都行，一键式傻瓜烧录。

## 远程控制

主要讲述在无屏幕、无线连接的情况下如何连接远程控制树莓派，买树莓派还单独买一块屏幕的话就很不划算了，毕竟要用到屏幕的地方真的不多，买来用一两次估计也就成了电子垃圾了，所以不建议购买。

### ssh

远程控制前必须要让树莓派连接上网络并且获取到树莓派的ip地址，在刚才烧录完系统的micro-sd卡的boot分区下新建wpa_supplicant.conf文件，并写入如下内容：

```conf
ctrl_interface=DIR=/var/run/wpa_supplicant GROUP=netdev
update_config=1
country=CN

network={
	ssid="你的wifi1"
	psk="wifi密码"
	key_mgmt=WPA-PSK
	priority=2 # wifi优先级，数值越大越优先
}


network={
	ssid="无加密的wifi"
	key_mgmt=NONE
	priority=1
}
```

同时新建一个ssh文件，因为raspbian默认是不开启ssh的，而第一次登陆我们又要先登陆到ssh才好给树莓派安装或开启其他远程操控软件。

完事保存，插入树莓派，开机即可连接上wifi。可以到路由器上查看连接上的树莓派的ip，如果是笔记本开的热点的话，点开热点详情那里也可以查看到连接设备的ip。

打开windows10的命令行窗口或powershell（有ssh服务的就行），输入` ssh pi@树莓派的ip地址` 并回车，输入默认密码raspberry并回车，这时你就成功登陆到树莓派了。

### xdrp

当你已经成功登陆到树莓派了，这时可以考虑安装图形化的远程桌面控制啦，win10自带的远程桌面就很不错啦，无需安装额外的客户端，而且功能强大哟，可以发送组合键还可以共享剪切板，真香！

可以在命令行中输入`sudo apt install xdrp` 给树莓派安装，安装完成后，在win10中，crtl+r 运行mstsc，输入ip地址，用户名pi，密码raspberry（如果还没有改的话）就可以成功登陆啦。

### vnc

vnc是已经安装到树莓派中了，但是默认不开启，我们只需要开启vnc服务即可，树莓派命令行中输入`sudo raspi-config` 选中interfaces并回车，开启vnc服务即可。

开启完成后可在vnc客户端登录监控制树莓派，推荐使用real vnc viewer，登入的账号密码和ssh使用的也一样， 实际上都是linux的一个用户账号。如果登入以后出现了黑屏，无法显示的情况的话，到ssh中输入`sudo raspi-config` 回车并选中advanced setting，在里面选中video output，再选第二项tv啥的完成后，按右箭头选到finish，重启后问题就解决啦。

## 系统配置



### 卸载不需要的软件



### 换国内源



### 配置中文环境



### 安装常用软件



### 美化终端



## 系统备份

### 全盘备份



### 占用备份

