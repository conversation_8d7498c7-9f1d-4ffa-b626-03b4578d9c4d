//- Maps

$font-map: (
  "mono": (
    ui-monospace,
    SFMono-Regular,
    "SF Mono",
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    "Liberation Mono",
    monospace,
  ),
  "sans": (
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    "Noto Sans",
    Helvetica,
    Arial,
    "PingFang SC",
    "Hiragino Sans GB",
    STHeiti,
    "Microsoft YaHei",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
  ),
);

$size-map: (
  "font-min": 1rem,
  // 16px
  "font-max": 1.125rem,
  // 18px
  "line-height-min": 1.25,
  "line-height-max": 1.5,
  "width-min": 20rem,
  // 320px / 16px
  "width-max": 50rem,
  // 640px / 16px
);

// theme colors passed from build system and got from /themes.js

$primary: #000;
$secondary: #fff;
$stealthy: #585858;
$invisible: #f2f2f2;
$selection-base: #0064c1;
$link: #0064c1;
$link-hover: #f00000;
$link-visited: #8d39d0;
$alarm: #d00000;
$background: #efefef;
$code: #000;
$border: #d1d9e0;

//- Functions

@function font($key) {
  @return map-get($font-map, $key);
}

@function size($key) {
  @return map-get($size-map, $key);
}

//- Mixins

@mixin placeholder {
  &::-moz-placeholder {
    @content;
  }

  &::-webkit-input-placeholder {
    @content;
  }

  &:-ms-input-placeholder {
    @content;
  }
}
